import { db } from '../../utils/model';
import { logger } from '../../utils/logger';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { HttpError } from '../../utils/httpError';

export const resetStaffMonthlyCreditUsed = async (): Promise<number> => {
  try {
    logger.info('Starting monthly credit reset for all staff members');

    // Update all staff records to reset monthlyCreditUsed to 0.0
    const updateResult = await db.staff.updateMany({
      where: {
        isActive: true,
      },
      data: {
        monthlyCreditUsed: 0.0,
      },
    });

    logger.info(
      `Monthly credit reset completed. Updated ${updateResult.count} staff records`
    );

    return updateResult.count;
  } catch (error) {
    logger.error('Error resetting staff monthly credit usage:', error);
    throw error;
  }
};

/**
 * Service function for manual staff credit reset with permission check
 * This is used by the API endpoint for administrative purposes
 */
export const manualStaffCreditReset = async (staffId: number) => {
  await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);
  try {
    const resetCount = await resetStaffMonthlyCreditUsed();

    return {
      message: `Successfully reset monthly credit usage for ${resetCount} staff members`,
      count: resetCount,
    };
  } catch (error) {
    logger.error('Error during manual staff credit reset:', error);
    if (error instanceof HttpError) {
      throw error;
    }
    throw new HttpError('Failed to reset staff monthly credit usage', 400);
  }
};
