export const timestamp = () => {
  const now = Date.now();

  const date = new Date(now);
  const timestamp = `${date.getFullYear()}${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;

  return timestamp;
};

export const generateCode = (number: number) => {
  let uniquecode;
  let randomValues = '';
  const stringValues = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const sizeOfCharacter = stringValues.length;

  for (let i = 0; i < number; i++) {
    randomValues =
      randomValues +
      stringValues.charAt(Math.floor(Math.random() * sizeOfCharacter));
    uniquecode = randomValues;
  }
  return uniquecode;
};

export function generateOrderNumber(): string {
  const id = Math.floor(Math.random() * Math.pow(36, 6))
    .toString(36)
    .padStart(6, '0')
    .toUpperCase();
  return id;
}

// export const getInitials = (name: string, phone: string) => {
//   const initials = name
//     .trim()
//     .split(/\s+/)
//     .filter(Boolean)
//     .map((word) => word[0].toUpperCase())
//     .join('')
//     .slice(0, 3);

//   const remainingLength = 6 - initials.length;

//   const phoneDigits = phone.replace(/\D/g, '');
//   const lastDigits = phoneDigits
//     .slice(-remainingLength)
//     .padStart(remainingLength, '0');

//   return (initials + lastDigits).slice(0, 6);
// };

export const getInitials = (name: string, phone: string): string => {
  const nameLetters = name.replace(/\s+/g, '').toUpperCase().slice(0, 3);

  const phoneDigits = phone.replace(/\D/g, '');
  const lastThreeDigits = phoneDigits.slice(-3).padStart(3, '0');

  return nameLetters + lastThreeDigits;
};

export const packageBookingStatus = ['DRAFT', 'PENDING', 'COMPLETED'];
export const transactionStatus = [
  'SUCCESS',
  'PENDING',
  'FAILED',
  'CANCELLED',
  'PROCESSING',
  'REVERSED',
  'CONFIRMED',
];
export const innovatioIdeaStatus = [
  'DRAFT',
  'PENDING_REVIEW',
  'ACCEPTED',
  'REJECTED',
  'IMPLEMENTED',
];
export const transactionType = [
  'TRANSFER',
  'WITHDRAWAL',
  'PACKAGE',
  'CAFETERIA',
  'REFUND',
  'REWARD',
];

export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

/**
 * Creates a date filter object for database queries based on startDate and endDate parameters
 * @param startDate - Start date in YYYY-MM-DD format
 * @param endDate - End date in YYYY-MM-DD format
 * @param fieldName - The database field name to filter on (default: 'createdAt')
 * @returns An object to be used in a Prisma where clause, or an empty object if no dates provided
 * @throws HttpError if dates are invalid or if startDate is after endDate
 */
/**
 * Formats a date object into a readable string format
 * @param date - The date to format
 * @param includeTime - Whether to include the time in the formatted string
 * @returns A formatted date string
 */
export const formatDate = (
  date: Date,
  includeTime: boolean = false
): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  let formattedDate = `${day}/${month}/${year}`;

  if (includeTime) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    formattedDate += ` ${hours}:${minutes}`;
  }

  return formattedDate;
};

export const createDateFilter = (
  startDate?: string,
  endDate?: string,
  fieldName: string = 'createdAt'
) => {
  const { HttpError } = require('./httpError');

  // If no dates provided, return empty object
  if (!startDate && !endDate) {
    return {};
  }

  // Validate date format if provided
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // YYYY-MM-DD format

  if (startDate && !dateRegex.test(startDate)) {
    throw new HttpError('Invalid startDate format. Use YYYY-MM-DD', 400);
  }

  if (endDate && !dateRegex.test(endDate)) {
    throw new HttpError('Invalid endDate format. Use YYYY-MM-DD', 400);
  }

  const dateFilter: any = {};
  dateFilter[fieldName] = {};

  const isSameDay = startDate && endDate && startDate === endDate;

  if (isSameDay) {
    const startDateTime = new Date(startDate);
    startDateTime.setHours(0, 0, 0, 0);

    const endDateTime = new Date(endDate);
    endDateTime.setHours(23, 59, 59, 999);

    dateFilter[fieldName].gte = startDateTime;
    dateFilter[fieldName].lte = endDateTime;

    return dateFilter;
  }

  // Ensure startDate is before or equal to endDate if both are provided
  if (startDate && endDate) {
    const startDateTimeCheck = new Date(startDate);
    const endDateTimeCheck = new Date(endDate);
    if (startDateTimeCheck > endDateTimeCheck) {
      throw new HttpError('startDate must be before or equal to endDate', 400);
    }
  }

  if (startDate) {
    const startDateTime = new Date(startDate);
    startDateTime.setHours(0, 0, 0, 0);
    dateFilter[fieldName].gte = startDateTime;
  }

  if (endDate) {
    const endDateTime = new Date(endDate);
    endDateTime.setHours(23, 59, 59, 999);
    dateFilter[fieldName].lte = endDateTime;
  }

  return dateFilter;
};

export const createCommonDateFilter = (
  keyword?: 'today' | 'yesterday' | 'last7days' | 'thisMonth' | 'lastMonth',
  fieldName: string = 'createdAt'
): Record<string, any> => {
  let startDate: Date | undefined;
  let endDate: Date | undefined;

  const today = new Date();

  // Reset time to 00:00:00.000 for start
  const setStartOfDay = (date: Date): Date => {
    return new Date(date.setHours(0, 0, 0, 0));
  };

  // Set time to 23:59:59.999 for end
  const setEndOfDay = (date: Date): Date => {
    return new Date(date.setHours(23, 59, 59, 999));
  };

  switch (keyword) {
    case 'today': {
      startDate = setStartOfDay(new Date(today));
      endDate = setEndOfDay(new Date(today));
      break;
    }

    case 'yesterday': {
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      startDate = setStartOfDay(new Date(yesterday));
      endDate = setEndOfDay(new Date(yesterday));
      break;
    }

    case 'last7days': {
      const start = new Date(today);
      start.setDate(today.getDate() - 6);
      startDate = setStartOfDay(start);
      endDate = setEndOfDay(today);
      break;
    }

    case 'thisMonth': {
      const firstOfThisMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        1
      );
      startDate = setStartOfDay(firstOfThisMonth);
      endDate = setEndOfDay(today);
      break;
    }

    case 'lastMonth': {
      const firstOfLastMonth = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        1
      );
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0); // last day of last month
      startDate = setStartOfDay(firstOfLastMonth);
      endDate = setEndOfDay(endOfLastMonth);
      break;
    }

    default:
      return {};
  }

  return {
    [fieldName]: {
      gte: startDate.toISOString(),
      lte: endDate.toISOString(),
    },
  };
};

export const numberFormat = (value: any) =>
  new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
  }).format(typeof value === 'string' ? parseFloat(value) : value);

export function toTitleCase(input: string) {
  return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
}

export const PaymentType = [
  'TRANSFER',
  'CARD',
  'WALLET',
  'CREDIT',
  'PATIENT',
  'VOUCHER',
];
export const OrderType = ['DINE_IN', 'TAKEAWAY', 'DELIVERY'];
export const SaleType = ['STAFF', 'PATIENT', 'SPECIAL', 'GENERAL'];
