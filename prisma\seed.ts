import { db } from '../src/v1/utils/model';
import * as bcrypt from 'bcryptjs';

async function main() {
  // const permissions = [
  //   'dashboard:view',

  //   'package:view',
  //   'package:create',
  //   'package:edit',
  //   'package:delete',

  //   'location:create',
  //   'location:view',
  //   'location:edit',
  //   'location:region',
  //   'location:all',

  //   'incident:create',
  //   'incident:view',
  //   'incident:edit',
  //   'incident:submit',
  //   'incident:approve',
  //   'incident:delete',
  //   'incident:comment',
  //   'incident:assign',
  //   'incident:close',

  //   'reward:view',
  //   'reward:create',
  //   'reward:edit',
  //   'reward:delete',

  //   'referral:view',
  //   'referral:create',
  //   'referral:edit',
  //   'referral:delete',

  //   'transaction:view',
  //   'transaction:create',
  //   'transaction:edit',

  //   'staff:create',
  //   'staff:edit',
  //   'staff:edit_own',
  //   'staff:view',

  //   'feedback:view',

  //   'settings:view',
  //   'settings:create',
  //   'settings:edit',
  //   'settings:delete',

  //   'requisition:create',
  //   'requisition:view',
  //   'requisition:edit',
  //   'requisition:submit',
  //   'requisition:approve',
  //   'requisition:delete',
  //   'requisition:cancel',

  //   'forum:view',
  //   'forum:create_group ',
  //   'forum:view_group',
  //   'forum:edit_group',
  //   'forum:delete_group',
  //   'forum:add_members',
  //   'forum:remove_members',
  //   'forum:join_group',
  //   'forum:send_message',
  //   'forum:view_messages',
  //   'forum:edit_message',
  //   'forum:delete_message',
  //   'forum:moderate_messages',
  //   'forum:pin_message',

  //   'patient:view',
  //   'patient:create',
  //   'patient:edit',
  //   'patient:delete',

  //   'appointment:view',
  //   'appointment:create',
  //   'appointment:edit',
  //   'appointment:delete',
  //   'appointment:reschedule',
  //   'appointment:cancel',

  //   'role:view',
  //   'role:create',
  //   'role:edit',
  //   'role:delete',

  //   'permission:view',
  //   'permission:create',
  //   'permission:edit',
  //   'permission:delete',

  //   'medical_record:view',
  //   'medical_record:create',
  //   'medical_record:edit',

  //   'doctor:view',
  //   'doctor:create',
  //   'doctor:edit',

  //   'hub:view',
  //   'hub:create',
  //   'hub:edit',

  //   'process:view',
  //   'process:create',
  //   'process:edit',

  //   'interaction:view',
  //   'interaction:create',
  //   'interaction:edit',
  //   'interaction:reply',

  //   'cafeteria:view',
  //   'cafeteria:inventory_manage',
  //   'cafeteria:menu_manage',
  //   'cafeteria:orders_manage',
  //   'cafeteria:pos_access',
  //   'cafeteria:special_approve',

  //   'chis:view',
  //   'chis:create',
  //   'chis:edit',
  //   'chis:approve',
  //   'chis:generate_code',

  //   'ai_assistant:view',
  //   'ai_assistant:chat',
  // ];

  // for (const permission of permissions) {
  //   await db.permission.upsert({
  //     where: { action: permission },
  //     update: {},
  //     create: {
  //       action: permission,
  //     },
  //   });
  // }

  // const permissionIds = await db.permission.findMany({
  //   select: { id: true },
  // });

  // const superAdminRole = await db.role.upsert({
  //   where: { name: 'superadmin' },
  //   update: {
  //     permissions: {
  //       set: [],
  //       connect: permissionIds,
  //     },
  //   },
  //   create: {
  //     name: 'super_admin',
  //     description: 'Has full access to manage all resources',
  //     permissions: {
  //       connect: permissionIds,
  //     },
  //   },
  // });

  // await db.staff.create({
  //   data: {
  //     locationId: 1,
  //     departmentId: 1,
  //     fullName: 'Mart Malde',
  //     phoneNumber: '***********',
  //     email: '<EMAIL>',
  //     password: await bcrypt.hash('MartmalD', 10),
  //     staffCode: '25/1234',
  //     roles: {
  //       connect: [{ id: superAdminRole?.id }],
  //     },
  //   },
  // });

  try {
    const staffWithoutAccounts = await db.staff.findMany({
      where: {
        locked: true,
      },
    });

    for (const staff of staffWithoutAccounts) {
      await db.staff.update({
        where: { id: staff.id },
        data: { locked: false },
      });
    }
  } catch (error) {
    console.log('Error emcountered ');
  }
}
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await db.$disconnect();
  });
