import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { logger } from '../../utils/logger';

export const rewardService = {
  getAllReward: async (staffId: any, query: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);
      const deactivated = query.status as string | undefined;

      const status =
        deactivated === 'true'
          ? true
          : deactivated === 'false'
            ? false
            : undefined;
      return db.reward.findMany({
        where: {
          deactivated: status,
        },
      });
    } catch (error) {
      logger.error('Failed to get all reward', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get all reward', 400);
    }
  },

  updateReward: async (staffId: any, reqBody: any) => {
    try {
      const updateData = { ...reqBody };
      await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

      const checkReward = await db.reward.findFirst({
        where: { id: Number(reqBody.id) },
      });
      if (!checkReward) {
        throw new HttpError('Reward type does not exist', 400);
      }
      await db.reward.update({
        where: { id: Number(reqBody.id) },
        data: updateData,
      });
      return { message: 'Reward updated successfully' };
    } catch (error) {
      logger.error('Failed to update reward', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update reward', 400);
    }
  },

  createReward: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);
      const checkReward = await db.reward.findFirst({
        where: {
          name: reqBody.name,
          deactivated: false,
        },
      });
      if (checkReward) {
        throw new HttpError(
          'Active reward type with this name already exists',
          400
        );
      }
      return db.reward.create({
        data: reqBody,
      });
    } catch (error) {
      logger.error('Failed to create reward', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create reward', 400);
    }
  },

  deleteReward: async (staffId: any, rewardId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_DELETE);

      const checkReward = await db.reward.findUnique({
        where: { id: Number(rewardId) },
      });
      if (!checkReward) {
        throw new HttpError('Reward type does not exist', 400);
      }
      await db.reward.delete({
        where: { id: Number(rewardId) },
      });
      return { message: 'Reward deleted successfully' };
    } catch (error) {
      logger.error('Failed to delete reward', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete reward', 400);
    }
  },

  verifyReferralCode: async (reqBody: any) => {
    try {
      const refCode = formatString.formatUpperCase(reqBody.code);
      const checkStaffCode = await db.referralCode.findUnique({
        where: { code: refCode },
      });
      if (!checkStaffCode) {
        throw new HttpError('Referral code does not exist', 400);
      }

      if (!checkStaffCode.isActive) {
        throw new HttpError('Referral code has been deactivated', 400);
      }

      return { message: 'Code still active' };
    } catch (error) {
      logger.error('Failed to verify referral code', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify referral code', 400);
    }
  },
};
