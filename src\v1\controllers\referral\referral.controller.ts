import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { adminReferralService } from '../../services/referral/admin';
import { referralService } from '../../services/referral/refferal';
import { referrerService } from '../../services/referral/referrer';
import { userService } from '../../services/booking/user';

//Referrals
const ListAllReferralHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as string;
  controllerOperations(
    adminReferralService.getAllReferrals,
    req.query,
    res,
    staffId
  );
};

const ListReferringEntitiesHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as string;
  controllerOperations(
    adminReferralService.getAllReferringEntities,
    req.query,
    res,
    staffId
  );
};


const ConfirmReferringEntityHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as string;
  controllerOperations(referrerService.confirmAccount, req.body, res, staffId);
};

const RegisterHandler = (req: Request, res: Response) => {
  controllerOperations(
    referrerService.registerAccount,
    undefined,
    res,
    req.body
  );
};

const SetPasswordHandler = (req: Request, res: Response) => {
  controllerOperations(referrerService.setPassword, undefined, res, req.body);
};

const LoginHandler = (req: Request, res: Response) => {
  controllerOperations(referrerService.loginAccount, undefined, res, req.body);
};

const validateAccountHandler = (req: Request, res: Response) => {
  const { validateAccount } = req.params;
  controllerOperations(
    referrerService.validateAccount,
    undefined,
    res,
    validateAccount
  );
};

const AccountProfile = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(referrerService.getProfile, undefined, res, accountId);
};

const ListReferralHandler = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(referralService.getReferrals, req.query, res, accountId);
};

const ReferralStats = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(
    referralService.referralStats,
    undefined,
    res,
    accountId
  );
};

const ReferPatient = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(referralService.newReferral, accountId, res, req.body);
};

const CheckExistitngPatient = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(userService.checkUser, req.query, res, accountId);
};

const SingleReferralhandler = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  const { referralId } = req.params;
  controllerOperations(
    referralService.singleReferral,
    referralId,
    res,
    accountId
  );
};

const ListPublicEntitiesHandler = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(
    adminReferralService.getPublicReferralEntities,
    req.query,
    res,
    accountId
  );
};

const UpdateReferralHandler = (req: Request, res: Response) => {
  const accountId = req.Referrer as unknown as string;
  controllerOperations(
    referralService.updateReferral,
    accountId,
    res,
    req.body
  );
};

const ReferralCommentHandler = (req: Request, res: Response) => {
  controllerOperations(referralService.postComment, undefined, res, req.body);
};

export const referralControllers = {
  ListAllReferralHandler,
  ListReferringEntitiesHandler,
  ConfirmReferringEntityHandler,
  RegisterHandler,
  LoginHandler,
  validateAccountHandler,
  SetPasswordHandler,
  AccountProfile,
  ReferPatient,
  ReferralStats,
  ListReferralHandler,
  SingleReferralhandler,
  ListPublicEntitiesHandler,
  CheckExistitngPatient,
  UpdateReferralHandler,
  ReferralCommentHandler,
};
