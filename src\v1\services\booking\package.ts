import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { userService } from './user';
import { packageBookingStatus } from '../../utils/util';
import {
  generateCode,
  timestamp,
  formatDate,
  numberFormat,
} from '../../utils/util';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { createDateFilter } from '../../utils/util';
import { settingsService } from '../settings';
import { logger } from '../../utils/logger';
import config from '../../../config/app.config';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { getMainSystemAccount } from '../system';

import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';

export const packageBookingService = {
  getAllPackageBookings: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.BOOKING_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          packageLocation: {
            location: {
              regionId,
            },
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = {
          packageLocation: {
            locationId,
          },
        };
      }

      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const status = query.status;
      const search = query.search as string;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      if (status && !packageBookingStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid booking status', 400);
      }

      // const cacheKey = `packageBookings:${page}:${limit}:${status}:${search}`;
      // const cachedData = await getCache(cacheKey);

      // if (cachedData) {
      //   return JSON.parse(cachedData as string);
      // }

      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { location: { contains: search, mode: 'insensitive' } },
                { bookingRef: { contains: search, mode: 'insensitive' } },
                { referralCode: { contains: search, mode: 'insensitive' } },
                { voucherCode: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {}),
        ...locationFilter,
        ...(status ? { bookingStatus: status.toUpperCase() } : {}),
        ...dateFilter,
      };

      const [bookings, totalPages, totalCount] = await db.$transaction([
        db.packageBooking.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            packages: {
              select: {
                name: true,
                slug: true,
                packageImage: true,
              },
            },
            user: true,
          },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.packageBooking.count({
          where: whereClause,
        }),
        db.packageBooking.count(),
      ]);

      const result = {
        bookings: bookings,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
      };

      // await setCache(cacheKey, JSON.stringify(result), 60 * 5); // Cache for 5 minutes

      return result;
    } catch (error) {
      logger.error('Error getting all package bookings:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch package bookings', 400);
    }
  },

  getPackageBookingById: async (bookingId: string) => {
    try {
      const cacheKey = `packageBooking:${bookingId}`;
      const cachedBooking = await getCache(cacheKey);

      if (cachedBooking) {
        return JSON.parse(cachedBooking as string);
      }

      const booking = await db.packageBooking.findUnique({
        where: { id: bookingId },
        include: {
          packages: {
            select: {
              id: true,
              name: true,
              slug: true,
              packageImage: true,
            },
          },
        },
      });
      if (!booking) {
        throw new HttpError('Package booking does not exist', 400);
      }

      await setCache(cacheKey, JSON.stringify(booking), 60 * 5); // Cache for 15 minutes

      return booking;
    } catch (error) {
      logger.error('Error getting package booking by ID:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch package booking', 400);
    }
  },

  bookPackage: async (reqBody: any) => {
    try {
      const {
        packageId,
        packageLocationId,
        referralCode,
        amount,
        location,
        ...userData
      } = reqBody;
      const user = await userService.checkAndCreateUser(userData);
      const bookingId = `PB-${timestamp()}${generateCode(5)?.toUpperCase()}`;

      const bookingExist = await db.user.findFirst({
        where: { emailAddress: user.emailAddress },
        include: {
          bookings: {
            include: {
              packageLocation: {
                select: {
                  id: true,
                },
              },
              packages: {
                select: {
                  id: true,
                  name: true,
                  packageImage: true,
                  totalSlot: true,
                },
              },
              user: {
                select: {
                  phoneNumber: true,
                  uhid: true,
                  emailAddress: true,
                },
              },
            },
          },
        },
      });

      if (bookingExist && bookingExist.bookings) {
        // Check if all bookingStatus are not 'completed'
        const draftBookings = bookingExist.bookings.find(
          (booking) => booking.bookingStatus === 'DRAFT'
        );
        if (draftBookings) {
          const sameBooking = packageId === draftBookings.packageId;
          return {
            code: 201,
            message: sameBooking
              ? 'An existing booking attempt for this package is still in progress.'
              : 'You have a package booking that has not been completed.',
            booking: draftBookings,
          };
        } else {
          const booking = await db.packageBooking.create({
            data: {
              packageLocationId: packageLocationId,
              packageId: packageId,
              amount: amount,
              referralCode: referralCode,
              userId: user.id,
              location: location,
              bookingRef: bookingId,
            },
            include: {
              packageLocation: {
                select: {
                  id: true,
                },
              },
              packages: {
                select: {
                  id: true,
                  name: true,
                  packageImage: true,
                  totalSlot: true,
                },
              },
              user: {
                select: {
                  phoneNumber: true,
                  uhid: true,
                  emailAddress: true,
                },
              },
            },
          });

          // Invalidate cache for getAllPackageBookings
          await deleteCacheByPattern('packageBookings:*');

          return {
            code: 200,
            booking: booking,
            message: 'Package booked successfully',
          };
        }
      }
    } catch (error) {
      logger.error('Error booking package:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to book package', 400);
    }
  },

  updateBooking: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.BOOKING_UPDATE);
      const booking = await db.packageBooking.findUnique({
        where: { id: reqBody.bookingId },
      });
      if (!booking) {
        throw new HttpError('Package booking does not exist', 400);
      }
      if (booking.bookingStatus !== 'PENDING') {
        throw new HttpError('Package booking cannot be updated', 400);
      }
      await db.packageBooking.update({
        where: { id: reqBody.bookingId },
        data: {
          bookingStatus: 'COMPLETED',
          updatedAt: new Date(),
          updatedBy: reqBody.updatedBy,
        },
      });

      // Invalidate cache for this booking and all package bookings
      await deleteCache(`packageBooking:${reqBody.bookingId}`);
      await deleteCacheByPattern('packageBookings:*');

      return { message: 'Package booked successfully' };
    } catch (error) {
      logger.error('Error updating package booking:', error);
      throw new HttpError('Failed to update package booking', 400);
    }
  },

  completePackageBooking: async (reqBody: any) => {
    try {
      const bookingExist = await db.packageBooking.findUnique({
        where: { id: reqBody.bookingId },
        include: {
          packages: true,
          user: {
            include: {
              account: true,
            },
          },
          packageLocation: true,
        },
      });
      if (!bookingExist) {
        throw new HttpError('Package booking does not exist', 400);
      }

      const systemAccount = await getMainSystemAccount();

      const emailName = bookingExist?.user?.uhid
        ? bookingExist?.user?.emailAddress
        : bookingExist?.user?.firstName;

      const [transaction, booking] = await db.$transaction([
        db.packageBooking.update({
          where: { id: reqBody.bookingId },
          include: {
            packages: true,
          },
          data: {
            bookingStatus: reqBody.bookingStatus,
            totalAmount: reqBody.amount,
            voucherCode: reqBody.voucherCode,
            voucherDiscount: reqBody.voucherDiscount,
          },
        }),

        db.transaction.create({
          data: {
            amount: reqBody.amount,
            status: 'SUCCESS',
            type: 'PACKAGE',
            mode: reqBody.transactionType,
            locationId: bookingExist.packageLocation.locationId,
            reference: reqBody.transactionReference,
            remarks: `Payment for ${bookingExist?.packages.name}`,
            fromAccountId: bookingExist.user.account?.id,
            toAccountId: systemAccount?.account?.id,
          },
        }),
        db.package.update({
          where: {
            id: bookingExist?.packageId,
          },
          data: {
            totalSlot: { decrement: 1 },
          },
        }),
      ]);
      if (reqBody.voucherCode && reqBody.voucherDiscount) {
        await db.discountRecord.create({
          data: {
            userId: bookingExist?.user?.id,
            package: bookingExist?.packages.name,
            locationId: bookingExist.packageLocation.locationId,
            code: reqBody.voucherCode,
            discountAmount: reqBody.voucherDiscount,
          },
        });
      }

      if (bookingExist.packages.bonusApplicable && bookingExist.referralCode) {
        const validReferralCode = await db.referralCode.findUnique({
          where: { code: bookingExist.referralCode },
          include: {
            reward: true,
            assignedToStaff: { select: { account: true } },
          },
        });

        if (validReferralCode && validReferralCode.isActive) {
          const amount = Number(bookingExist?.amount);

          await Promise.all(
            validReferralCode.reward.map(async (refCodeReward) => {
              const reward =
                refCodeReward.name === 'STAFF_PACKAGE_REFERRAL_BONUS';
              if (!reward) {
                return;
              }
              const rewardValue =
                refCodeReward.type === 'PERCENTAGE'
                  ? (refCodeReward.value / 100) * amount
                  : refCodeReward.type === 'CASH'
                    ? refCodeReward.value
                    : 0;

              await Promise.all([
                db.referralCodeUsage.create({
                  data: {
                    referralCodeId: Number(validReferralCode.id),
                    dateUsed: new Date(),
                    purpose: bookingExist?.packages.name,
                    value: rewardValue,
                  },
                }),
                db.staff.update({
                  where: { id: validReferralCode?.assignedToStaffId },
                  data: {
                    wallet: {
                      increment: rewardValue,
                    },
                    total: {
                      increment: rewardValue,
                    },
                  },
                }),
                db.transaction.create({
                  data: {
                    amount: rewardValue,
                    status: 'SUCCESS',
                    type: 'REWARD',
                    mode: 'internal',
                    locationId: bookingExist.packageLocation.locationId,
                    reference: reqBody.transactionReference,
                    remarks: `Package Referral Bonus  - Payment for ${bookingExist?.packages.name}`,
                    fromAccountId: systemAccount?.account?.id,
                    toAccountId:
                      validReferralCode?.assignedToStaff?.account?.id,
                  },
                }),
              ]);
              // Notify staff with BOOKING_READ permission
              const staffWithBookingPermission = await db.staff.findMany({
                where: {
                  isActive: true,
                  roles: {
                    some: {
                      permissions: {
                        some: {
                          action: 'BOOKING_READ',
                        },
                      },
                    },
                  },
                },
                select: { id: true },
              });

              const staffIds = staffWithBookingPermission.map(
                (staff) => staff.id
              );
              if (staffIds.length > 0) {
                const { SocketNotificationService } = await import(
                  '../notification/socketNotificationService'
                );
                await SocketNotificationService.notifyMultipleUsers(
                  staffIds,
                  'new_package_booking',
                  {
                    title: 'New Package Booking',
                    message: `New booking for ${bookingExist.packages.name} at ${bookingExist.location}`,
                    type: 'info',
                    data: {
                      bookingId: booking.id,
                      packageName: bookingExist.packages.name,
                      customerName: bookingExist.user.emailAddress,
                      amount: bookingExist.amount,
                      location: bookingExist.location,
                    },
                  }
                );
              }

              // Notify staff about referral reward
              if (validReferralCode?.assignedToStaffId) {
                const { notificationService } = await import('../notification');
                await notificationService.createNotificationWithSocket(
                  validReferralCode.assignedToStaffId,
                  'Referral Bonus Received',
                  `You earned ${rewardValue} from referral code usage for ${bookingExist?.packages.name}`,
                  'reward',
                  'medium',
                  {
                    rewardValue,
                    packageName: bookingExist?.packages.name,
                    referralCode: bookingExist.referralCode,
                  }
                );
              }
            })
          );
        }
      }

      // Send confirmation email to customer
      const customerMailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: bookingExist?.user?.emailAddress,
        subject: 'Payment Successful: Your Health Package is Confirmed!',
        template: 'package',
        context: {
          name: emailName,
          package: bookingExist?.packages.name,
        },
      };

      if (reqBody.bookingStatus === 'PENDING') {
        enqueueSendEmailJob(customerMailOptions);
      }

      // Send notification emails to admins
      try {
        const adminEmails = await settingsService.getAdminNotificationEmails();

        if (adminEmails && adminEmails.length > 0) {
          logger.info(
            `Sending admin notification emails to ${adminEmails.length} recipients`
          );

          const customerName = bookingExist?.user?.firstName
            ? `${bookingExist?.user?.firstName} ${bookingExist?.user?.lastName || ''}`
            : bookingExist?.user?.uhid;

          // Create dashboard URL for the booking
          const dashboardUrl = `${config.ADMIN_DASHBOARD_BASE_URL}/packages`;

          const adminMailOptions = {
            from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
            to: adminEmails.join(', '),
            subject: 'URGENT: New Package Booking Notification',
            template: 'admin-notification',
            context: {
              package: bookingExist?.packages.name,
              customerName: customerName,
              customerEmail: bookingExist?.user?.emailAddress,
              bookingRef: bookingExist.bookingRef,
              amount: `${numberFormat(reqBody.amount)}`,
              date: formatDate(new Date(), true),
              dashboardUrl: dashboardUrl,
            },
          };
          if (reqBody.bookingStatus === 'PENDING') {
            enqueueSendEmailJob(adminMailOptions);
          }
        } else {
          logger.info(
            'No admin notification emails configured, skipping admin notification'
          );
        }
      } catch (error) {
        logger.error('Error sending admin notification emails:', error);
        // Don't throw error here, we still want to complete the booking process
      }

      // Invalidate cache for this booking and all package bookings
      await deleteCache(`packageBooking:${reqBody.bookingId}`);
      await deleteCacheByPattern('packageBookings:*');

      return {
        transaction,
        booking,
        message: 'Package booking completed successfully',
      };
    } catch (error) {
      logger.error('Error completing package booking:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to complete package booking', 400);
    }
  },

  deleteBooking: async (id: string) => {
    try {
      const booking = await db.packageBooking.findUnique({
        where: { id },
      });
      if (!booking) {
        throw new HttpError('Packing booking does not exist', 400);
      }
      if (booking && booking.bookingStatus === 'DRAFT') {
        await db.packageBooking.delete({
          where: { id },
        });

        // Invalidate cache for this booking and all package bookings
        await deleteCache(`packageBooking:${id}`);
        await deleteCacheByPattern('packageBookings:*');

        return { message: 'Package booking removed successfully' };
      } else {
        throw new HttpError('Packing booking cannot be removed', 400);
      }
    } catch (error) {
      logger.error('Error deleting package booking:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete package booking', 400);
    }
  },
};
