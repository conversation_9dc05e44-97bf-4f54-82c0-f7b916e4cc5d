import { db } from '../../utils/model';
import { logger } from '../../utils/logger';
import { getCafeteriaSystemAccount, getMainSystemAccount } from '../system';

export const specialOrderCronJobs = {
  autoReceiveDeliveredOrders: async () => {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const ordersToUpdate = await db.cafeteriaSpecialOrder.findMany({
        where: {
          status: 'DELIVERED',
          updatedAt: {
            lte: oneHourAgo,
          },
        },
      });

      if (ordersToUpdate.length > 0) {
        const cafeteriaSystemAccount = await getCafeteriaSystemAccount();
        const companySystemAccount = await getMainSystemAccount();

        await db.$transaction(async (tx) => {
          for (const order of ordersToUpdate) {
            await tx.cafeteriaSpecialOrder.update({
              where: { id: order.id },
              data: {
                status: 'RECEIVED',
                receivedAt: new Date(),
                receivedBy: 'System',
              },
            });

            await tx.transaction.create({
              data: {
                amount: order.totalAmount,
                status: 'SUCCESS',
                type: 'CAFETERIA',
                locationId: Number(order.locationId),
                mode: 'Special',
                reference: order.orderNumber,
                remarks: `Special order received - ${order.purpose}`,
                fromAccountId: companySystemAccount?.account?.id,
                toAccountId: cafeteriaSystemAccount?.account?.id,
              },
            });
          }
        });

        logger.info(`Auto-received ${ordersToUpdate.length} special orders`);
      }
    } catch (error) {
      logger.error('Error in auto-receive cron job:', error);
    }
  },
};
