import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter, innovatioIdeaStatus } from '../../utils/util';
import { logger } from '../../utils/logger';

export interface UpdateInnovationIdeaData {
  title?: string;
  description?: string;
  ideaId: number;
  status?: 'DRAFT' | 'PENDING_REVIEW' | 'ACCEPTED' | 'REJECTED' | 'IMPLEMENTED';
}

export const innovationIdeaService = {
  createInnovationIdea: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_CREATE);
      const { title, description, categoryId, tagIds } = reqBody;
      if (!title || !description) {
        throw new HttpError('Title and description are required', 400);
      }
      await db.innovationIdea.create({
        data: {
          title,
          description,
          status: 'DRAFT',
          authorId: staffId,
          categoryId: Number(categoryId),
          tags: {
            connect: tagIds.map((tagId: number) => ({
              id: Number(tagId),
            })),
          },
        },
      });
      return {
        message: 'Innovation idea submitted successfully',
      };
    } catch (error) {
      logger.error('Error submitting innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      // throw new HttpError('Failed to create innovation idea', 400);
    }
  },

  getAllCategories: async (staffId: number, query: any) => {
    try {
      return db.innovationCategory.findMany({
        select: {
          id: true,
          name: true,
        },
      });
    } catch (error) {
      logger.error('Error getting innovation categories:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation categories', 400);
    }
  },

  getAllTags: async (staffId: number, query: any) => {
    try {
      return db.innovationTag.findMany({
        select: {
          id: true,
          name: true,
        },
      });
    } catch (error) {
      logger.error('Error getting innovation tags:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation tags', 400);
    }
  },

  getInnovationIdeasStats: async (staffId: number, query: any) => {
    try {
      const [totalCount, groupedCounts] = await db.$transaction([
        db.innovationIdea.count(),
        db.innovationIdea.groupBy({
          by: ['status'],
          orderBy: {
            status: 'asc',
          },
          _count: true, // 👈 count all fields
        }),
      ]);

      const stats: Record<string, number> = {
        total: totalCount,
      };

      for (const item of groupedCounts) {
        const statusKey = item.status.toLowerCase();
        stats[statusKey] = item._count as number;
      }

      return stats;
    } catch (error) {
      logger.error('Error getting innovation ideas stats:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation ideas stats', 400);
    }
  },

  getAllInnovationIdeas: async (staffId: number, query: any) => {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        search,
        startDate,
        endDate,
        myIdeas,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const isMyIdeas = myIdeas === 'true';

      if (status && !innovatioIdeaStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid status value', 400);
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);
      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(search
          ? {
              OR: [
                { title: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
                {
                  author: {
                    fullName: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  category: {
                    name: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  tags: {
                    some: {
                      name: { contains: search, mode: 'insensitive' },
                    },
                  },
                },
              ],
            }
          : {}),
        ...dateFilter,
        ...(isMyIdeas ? { authorId: staffId } : {}),
      };

      const [ideas, total] = await Promise.all([
        db.innovationIdea.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
                email: true,
                department: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            comments: {
              select: {
                id: true,
                content: true,
                createdAt: true,
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              },
            },
            tags: {
              select: {
                id: true,
                name: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            likes: {
              select: {
                id: true,
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              },
            },
          },
        }),
        db.innovationIdea.count({ where: whereClause }),
      ]);

      return {
        ideas,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };
    } catch (error) {
      logger.error('Error getting innovation ideas:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation ideas', 400);
    }
  },

  updateInnovationIdea: async (
    staffId: number,
    reqBody: UpdateInnovationIdeaData
  ) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: Number(reqBody.ideaId) },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const updateData: any = {};

      if (reqBody.title) {
        updateData.title = reqBody.title.trim();
      }

      if (reqBody.description) {
        updateData.description = reqBody.description.trim();
      }

      if (reqBody.status) {
        updateData.status = reqBody.status;

        // Set appropriate timestamps based on status
        if (reqBody.status === 'PENDING_REVIEW' && !idea.reviewedAt) {
          updateData.reviewedAt = new Date();
        } else if (reqBody.status === 'ACCEPTED' && !idea.acceptedAt) {
          updateData.acceptedAt = new Date();
        } else if (reqBody.status === 'REJECTED' && !idea.rejectedAt) {
          updateData.rejectedAt = new Date();
        } else if (reqBody.status === 'IMPLEMENTED' && !idea.implementedAt) {
          updateData.implementedAt = new Date();
        }
      }

      const updatedIdea = await db.innovationIdea.update({
        where: { id: Number(reqBody.ideaId) },
        data: updateData,
      });

      return {
        message: 'Innovation idea updated successfully',
        data: updatedIdea,
      };
    } catch (error) {
      logger.error('Error updating innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update innovation idea', 400);
    }
  },

  deleteInnovationIdea: async (staffId: number, ideaId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      // Check if user is the author or has admin permissions
      if (idea.authorId !== staffId) {
        await staffHasPermission(staffId, PERMISSIONS.STAFF_DELETE);
      }

      await db.innovationIdea.delete({
        where: { id: ideaId },
      });

      return {
        message: 'Innovation idea deleted successfully',
      };
    } catch (error) {
      logger.error('Error deleting innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete innovation idea', 400);
    }
  },
};
