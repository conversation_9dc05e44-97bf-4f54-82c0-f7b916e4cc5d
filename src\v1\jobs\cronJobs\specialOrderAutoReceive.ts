import cron from 'node-cron';
import { specialOrderCronJobs } from '../../services/cafeteria/cronJobs';
import { logger } from '../../utils/logger';

export const setupSpecialOrderAutoReceiveJob =
  (): cron.ScheduledTask | null => {
    try {
      // Run every 30 minutes
      const task = cron.schedule(
        '*/30 * * * *',
        async () => {
          logger.info('Running special order auto-receive job...');
          await specialOrderCronJobs.autoReceiveDeliveredOrders();
        },
        {
          scheduled: false,
        }
      );

      task.start();
      logger.info(
        'Special order auto-receive cron job scheduled (every 30 minutes)'
      );

      return task;
    } catch (error) {
      logger.error(
        'Error setting up special order auto-receive cron job:',
        error
      );
      return null;
    }
  };
