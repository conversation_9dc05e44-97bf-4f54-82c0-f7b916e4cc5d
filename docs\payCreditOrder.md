# Pay Credit Function

## Overview
The `payCredit` function allows authorized staff members to credit a staff member's meal voucher and automatically clear their outstanding credit orders in the cafeteria system.

## Endpoint
```
POST /api/v1/staff/pay-credit
```

## Authentication
Requires staff authentication with `CAFETERIA_POS_ACCESS` permission.

## Request Body
```json
{
  "staffCode": "STAFF001",
  "amount": 100.00,
  "mode": "CASH"
}
```

### Parameters
- `staffCode` (string, required): The unique staff code of the staff member to credit
- `amount` (number, required): The amount to credit to the staff member's meal voucher
- `mode` (string, required): The payment mode used for the credit (e.g., "CASH", "TRANSFER", "CARD")

## Response
```json
{
  "success": true,
  "data": {
    "message": "Credit payment processed successfully",
    "creditedAmount": "100.00",
    "existingMealVoucher": "25.00",
    "totalAvailableCredit": "125.00",
    "ordersCleared": 2,
    "totalOrdersCleared": "80.00",
    "remainingMealVoucher": "45.00"
  }
}
```

### Response Fields
- `message`: Success message
- `creditedAmount`: Amount that was newly credited to the meal voucher
- `existingMealVoucher`: Amount that was already in the meal voucher before this transaction
- `totalAvailableCredit`: Total credit available (new credit + existing voucher balance)
- `ordersCleared`: Number of credit orders that were cleared
- `totalOrdersCleared`: Total amount of orders that were cleared
- `remainingMealVoucher`: Amount remaining in meal voucher after clearing orders

## Functionality

### Credit Process
1. **Validation**: Validates staff permissions, target staff existence, and credit amount
2. **Credit Addition**: Adds the specified amount to the staff member's meal voucher
3. **Transaction Recording**: Creates a transaction record for the credit operation
4. **Order Clearing**: Automatically clears outstanding credit orders using the credited amount

### Order Clearing Logic
- **Existing Balance Consideration**: Any existing meal voucher balance is added to the new credit amount for total available credit
- Orders are processed in chronological order (oldest first)
- Only orders with `paymentType: 'CREDIT'` and `creditPaid: false` are considered
- Orders are cleared completely - partial payments are not supported
- If the total available credit cannot cover an order completely, that order and subsequent orders remain unpaid
- The meal voucher balance is set to the remaining amount after clearing orders
- **Transaction Updates**: When orders are marked as paid, corresponding transaction records with matching order numbers are also updated to set `creditPaid: true` and remarks are appended with payment information

### Example Scenarios

#### Scenario 1: Full Order Clearing with Existing Balance
- Staff has existing meal voucher: ₦25
- Staff has 2 unpaid credit orders: ₦50 and ₦30
- New credit amount: ₦100
- Total available: ₦125 (₦25 + ₦100)
- Result: Both orders cleared (₦80), ₦45 remains in meal voucher

#### Scenario 2: Partial Order Clearing
- Staff has existing meal voucher: ₦10
- Staff has 3 unpaid credit orders: ₦50, ₦30, and ₦40
- New credit amount: ₦60
- Total available: ₦70 (₦10 + ₦60)
- Result: First two orders cleared (₦80 total), but only ₦70 available, so only first order (₦50) cleared, ₦20 remains

#### Scenario 3: Insufficient Credit
- Staff has existing meal voucher: ₦20
- Staff has 1 unpaid credit order: ₦100
- New credit amount: ₦50
- Total available: ₦70 (₦20 + ₦50)
- Result: No orders cleared (insufficient for ₦100 order), ₦70 remains in meal voucher

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Credit amount must be greater than 0"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Staff member not found or inactive"
}
```

### 400 Bad Request (Locked Account)
```json
{
  "success": false,
  "message": "Staff account is locked"
}
```

## Database Operations
The function performs the following database operations in a transaction:

1. **Staff Update**: Increments the `mealVoucher` field by the credit amount
2. **Transaction Creation**: Creates a transaction record with type `PURCHASE`
3. **Order Updates**: Updates qualifying orders to set `creditPaid: true` and `creditPaymentMethod` to the specified mode
4. **Transaction Updates**: Updates corresponding transaction records (matching order numbers) to set `creditPaid: true` and appends to existing remarks
5. **Voucher Balance Update**: Sets the `mealVoucher` to the final remaining balance after considering existing balance and clearing orders

## Usage Example

```javascript
// Using fetch API
const response = await fetch('/api/v1/staff/pay-credit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    staffCode: 'STAFF001',
    amount: 150.00,
    mode: 'CASH'
  })
});

const result = await response.json();
console.log(result);
```

## Notes
- All monetary amounts are handled using decimal precision to avoid floating-point errors
- The function maintains data consistency through database transactions
- Transaction references are automatically generated with format `CRD-{timestamp}{randomCode}`
- The system account is used as the source for credit transactions
