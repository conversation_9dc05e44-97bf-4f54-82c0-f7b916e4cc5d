# Pay Credit Order Function

## Overview
The `payCreditOrder` function allows authorized staff members to credit a staff member's meal voucher and automatically clear their outstanding credit orders in the cafeteria system.

## Endpoint
```
POST /api/v1/staff/pay-credit-order
```

## Authentication
Requires staff authentication with `CAFETERIA_POS_ACCESS` permission.

## Request Body
```json
{
  "staffCode": "STAFF001",
  "amount": 100.00
}
```

### Parameters
- `staffCode` (string, required): The unique staff code of the staff member to credit
- `amount` (number, required): The amount to credit to the staff member's meal voucher

## Response
```json
{
  "success": true,
  "data": {
    "message": "Credit payment processed successfully",
    "creditedAmount": "100.00",
    "ordersCleared": 2,
    "totalOrdersCleared": "80.00",
    "remainingMealVoucher": "20.00"
  }
}
```

### Response Fields
- `message`: Success message
- `creditedAmount`: Total amount credited to the meal voucher
- `ordersCleared`: Number of credit orders that were cleared
- `totalOrdersCleared`: Total amount of orders that were cleared
- `remainingMealVoucher`: Amount remaining in meal voucher after clearing orders

## Functionality

### Credit Process
1. **Validation**: Validates staff permissions, target staff existence, and credit amount
2. **Credit Addition**: Adds the specified amount to the staff member's meal voucher
3. **Transaction Recording**: Creates a transaction record for the credit operation
4. **Order Clearing**: Automatically clears outstanding credit orders using the credited amount

### Order Clearing Logic
- Orders are processed in chronological order (oldest first)
- Only orders with `paymentType: 'CREDIT'` and `creditPaid: false` are considered
- Orders are cleared completely - partial payments are not supported
- If the credited amount cannot cover an order completely, that order and subsequent orders remain unpaid
- The meal voucher balance is reduced by the total amount used to clear orders

### Example Scenarios

#### Scenario 1: Full Order Clearing
- Staff has 2 unpaid credit orders: ₦50 and ₦30
- Credit amount: ₦100
- Result: Both orders cleared, ₦20 remains in meal voucher

#### Scenario 2: Partial Order Clearing
- Staff has 3 unpaid credit orders: ₦50, ₦30, and ₦40
- Credit amount: ₦60
- Result: Only first order (₦50) cleared, ₦10 remains in meal voucher

#### Scenario 3: Insufficient Credit
- Staff has 1 unpaid credit order: ₦100
- Credit amount: ₦50
- Result: No orders cleared, ₦50 added to meal voucher

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Credit amount must be greater than 0"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Staff member not found or inactive"
}
```

### 400 Bad Request (Locked Account)
```json
{
  "success": false,
  "message": "Staff account is locked"
}
```

## Database Operations
The function performs the following database operations in a transaction:

1. **Staff Update**: Increments the `mealVoucher` field by the credit amount
2. **Transaction Creation**: Creates a transaction record with type `REWARD`
3. **Order Updates**: Updates qualifying orders to set `creditPaid: true` and `creditPaymentMethod: 'VOUCHER'`
4. **Voucher Deduction**: Decrements the `mealVoucher` by the amount used to clear orders

## Usage Example

```javascript
// Using fetch API
const response = await fetch('/api/v1/staff/pay-credit-order', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    staffCode: 'STAFF001',
    amount: 150.00
  })
});

const result = await response.json();
console.log(result);
```

## Notes
- All monetary amounts are handled using decimal precision to avoid floating-point errors
- The function maintains data consistency through database transactions
- Transaction references are automatically generated with format `CRD-{timestamp}{randomCode}`
- The system account is used as the source for credit transactions
