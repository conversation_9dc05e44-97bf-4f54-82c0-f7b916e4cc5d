# Pay Credit Function

## Overview
The `payCredit` function allows authorized staff members to credit a staff member's meal voucher and automatically clear their outstanding credit orders in the cafeteria system.

## Endpoint
```
POST /api/v1/staff/pay-credit
```

## Authentication
Requires staff authentication with `CAFETERIA_POS_ACCESS` permission.

## Request Body
```json
{
  "staffCode": "STAFF001",
  "amount": 100.00,
  "mode": "CASH"
}
```

### Parameters
- `staffCode` (string, required): The unique staff code of the staff member to credit
- `amount` (number, required): The amount to credit to the staff member's meal voucher
- `mode` (string, required): The payment mode used for the credit (e.g., "CASH", "TRANSFER", "CARD")

## Response
```json
{
  "success": true,
  "data": {
    "message": "Credit payment processed successfully",
    "paymentBreakdown": [
      "Wallet: ₦50.00",
      "Existing Meal Voucher: ₦25.00",
      "New Meal Voucher Credit: ₦5.00"
    ],
    "walletUsed": "50.00",
    "existingVoucherUsed": "25.00",
    "newVoucherCredited": "5.00",
    "ordersCleared": 2,
    "totalOrdersCleared": "80.00",
    "finalWalletBalance": "0.00",
    "finalMealVoucherBalance": "0.00",
    "remainingUnpaidOrders": 0
  }
}
```

### Response Fields
- `message`: Success message
- `paymentBreakdown`: Array showing the breakdown of payment sources used
- `walletUsed`: Amount deducted from wallet balance
- `existingVoucherUsed`: Amount used from existing meal voucher balance
- `newVoucherCredited`: Amount newly credited to meal voucher (only if needed)
- `ordersCleared`: Number of credit orders that were cleared
- `totalOrdersCleared`: Total amount of orders that were cleared
- `finalWalletBalance`: Wallet balance after the transaction
- `finalMealVoucherBalance`: Meal voucher balance after the transaction
- `remainingUnpaidOrders`: Number of credit orders that remain unpaid

## Functionality

### Credit Process
1. **Validation**: Validates staff permissions, target staff existence, and credit amount
2. **Payment Priority Analysis**: Determines optimal payment breakdown using priority order:
   - **First Priority**: Wallet balance
   - **Second Priority**: Existing meal voucher balance
   - **Third Priority**: New meal voucher credit (only if needed)
3. **Balance Updates**: Updates wallet and meal voucher balances based on usage
4. **Transaction Recording**: Creates a detailed transaction record showing payment breakdown
5. **Order Clearing**: Clears outstanding credit orders using the available funds

### Payment Priority Logic
- **Step 1**: Calculate total unpaid credit orders amount
- **Step 2**: Determine payment breakdown using priority:
  1. **Wallet First**: Use available wallet balance
  2. **Existing Voucher Second**: Use existing meal voucher balance
  3. **New Credit Last**: Credit meal voucher only if wallet + existing voucher insufficient
- **Step 3**: Update balances based on usage:
  - Deduct used amount from wallet
  - Deduct used amount from existing meal voucher OR add new credit if needed
- **Step 4**: Process orders chronologically (oldest first)
- Only orders with `paymentType: 'CREDIT'` and `creditPaid: false` are considered
- Orders are cleared completely - partial payments are not supported
- **Payment Method Tracking**: Orders are marked with appropriate `creditPaymentMethod`:
  - `WALLET`: Paid using wallet balance only
  - `MEAL_VOUCHER`: Paid using existing meal voucher only
  - `NEW_VOUCHER`: Paid using newly credited meal voucher only
  - `MIXED`: Paid using combination of payment sources
- **Transaction Updates**: When orders are marked as paid, corresponding transaction records are updated with detailed payment breakdown in remarks

### Example Scenarios

#### Scenario 1: Wallet Sufficient
- Staff wallet: ₦100, meal voucher: ₦20
- Unpaid credit orders: ₦50, ₦30 (total ₦80)
- New credit offered: ₦50
- **Result**: Wallet used: ₦80, meal voucher: unchanged ₦20, new credit: ₦0
- **Final**: Wallet ₦20, meal voucher ₦20, all orders cleared

#### Scenario 2: Wallet + Existing Voucher Sufficient
- Staff wallet: ₦40, meal voucher: ₦50
- Unpaid credit orders: ₦60, ₦25 (total ₦85)
- New credit offered: ₦50
- **Result**: Wallet used: ₦40, existing voucher used: ₦45, new credit: ₦0
- **Final**: Wallet ₦0, meal voucher ₦5, all orders cleared

#### Scenario 3: Need New Credit
- Staff wallet: ₦30, meal voucher: ₦20
- Unpaid credit orders: ₦40, ₦30, ₦25 (total ₦95)
- New credit offered: ₦50
- **Result**: Wallet used: ₦30, existing voucher used: ₦20, new credit: ₦45
- **Final**: Wallet ₦0, meal voucher ₦45, all orders cleared

#### Scenario 4: Insufficient Even with New Credit
- Staff wallet: ₦10, meal voucher: ₦15
- Unpaid credit orders: ₦50, ₦40, ₦30 (total ₦120)
- New credit offered: ₦50
- **Result**: Wallet used: ₦10, existing voucher used: ₦15, new credit: ₦50 (total ₦75)
- **Final**: Wallet ₦0, meal voucher ₦50, first two orders cleared (₦90 needed but only ₦75 available, so only first order ₦50 cleared), ₦25 remaining credit

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Credit amount must be greater than 0"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Staff member not found or inactive"
}
```

### 400 Bad Request (Locked Account)
```json
{
  "success": false,
  "message": "Staff account is locked"
}
```

## Database Operations
The function performs the following database operations in a transaction:

1. **Wallet Update**: Decrements wallet balance by the amount used (if any)
2. **Meal Voucher Update**:
   - If new credit needed: Sets to `(existing + new credit - existing used)`
   - If only existing voucher used: Decrements by amount used
3. **Transaction Creation**: Creates a transaction record with detailed payment breakdown in remarks
4. **Order Updates**: Updates qualifying orders to set `creditPaid: true` and `creditPaymentMethod` based on payment sources used:
   - `WALLET`: If only wallet was used
   - `MEAL_VOUCHER`: If only existing meal voucher was used
   - `NEW_VOUCHER`: If only new voucher credit was used
   - `MIXED`: If multiple payment sources were used
5. **Transaction Updates**: Updates corresponding transaction records (matching order numbers) to set `creditPaid: true` and appends payment details to existing remarks

## Usage Example

```javascript
// Using fetch API
const response = await fetch('/api/v1/staff/pay-credit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    staffCode: 'STAFF001',
    amount: 150.00,
    mode: 'CASH'
  })
});

const result = await response.json();
console.log(result);
```

## Notes
- All monetary amounts are handled using decimal precision to avoid floating-point errors
- The function maintains data consistency through database transactions
- Transaction references are automatically generated with format `CRD-{timestamp}{randomCode}`
- The system account is used as the source for credit transactions
