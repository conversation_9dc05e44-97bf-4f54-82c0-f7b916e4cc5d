import { db, decimal } from '../../utils/model';
import * as bcrypt from 'bcryptjs';
import crypto from 'crypto';
import jwt, { Secret } from 'jsonwebtoken';
import config from '../../../config/app.config';
import { HttpError } from '../../utils/httpError';
import { messagingService } from '../messaging';
import { formatString } from '../../utils/stringFormatter';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { getInitials } from '../../utils/util';
import { createDateFilter } from '../../utils/util';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import { toTitleCase } from '../../utils/util';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { broadcast, sendToUser } from '../socket';
import { logger } from '../../utils/logger';

export const SECRET_KEY = config.SIGNING_TOKEN_SECRET as Secret;

interface CreateStaffRequestBody {
  email: string;
  staffCode: string;
  fullName: string;
  locationId?: number;
  departmentId: number;
  role?: string;
  type?: string;
  phoneNumber: string;
  isDoctor: boolean;
  rewardIds?: (string | number)[];
  roleIds: (string | number)[];
  specialtyId?: string | number;
  isConsultant?: boolean;
  isVisitingConsultant?: boolean;
  creditLimit: any;
}

// Helper function to clear all admin-related caches
export const clearStaffCaches = async (): Promise<void> => {
  await deleteCacheByPattern('staff:*');
};

export const staffService = {
  getAllStaff: async (staffId: number, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const canManage = await auth.hasPermission(PERMISSIONS.STAFF_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const search: string = (query.search as string) || '';
      const status = query.status;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      const skip = page && limit ? (page - 1) * limit : undefined;
      const take = limit ? limit : undefined;

      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { fullName: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
                { staffCode: { contains: search } },
                {
                  referralCode: {
                    code: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  location: { name: { contains: search, mode: 'insensitive' } },
                },
                {
                  department: {
                    name: { contains: search, mode: 'insensitive' },
                  },
                },
                { unit: { name: { contains: search, mode: 'insensitive' } } },
              ],
            }
          : {}),
        ...locationFilter,
        ...dateFilter,
        ...(typeof status === 'string' && status.trim().toLowerCase() === 'true'
          ? { isActive: true }
          : typeof status === 'string' &&
              status.trim().toLowerCase() === 'false'
            ? { isActive: false }
            : {}),
      };

      const [staffs, totalPages, totalCount] = await db.$transaction([
        db.staff.findMany({
          orderBy: { createdAt: 'desc' },
          ...(skip !== undefined && { skip }),
          ...(take !== undefined && { take }),
          where: whereClause,
          include: {
            referralCode: {
              select: {
                id: true,
                code: true,
                isActive: true,
                _count: {
                  select: {
                    referralUsages: true,
                  },
                },
              },
            },
            roles: {
              select: {
                name: true,
                permissions: {
                  select: {
                    action: true,
                  },
                },
              },
            },
            location: {
              select: {
                name: true,
                region: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            doctorProfile: {
              select: {
                isDoctor: true,
                isConsultant: true,
                isVisitingConsultant: true,
                specialty: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            department: {
              select: {
                name: true,
              },
            },
            unit: {
              select: {
                name: true,
              },
            },
            account: true,
            cafeteriaOrders: {
              where: {
                paymentType: 'CREDIT',
                creditPaid: false,
              },
              select: {
                totalAmount: true,
              },
            },
          },
        }),
        db.staff.count({
          where: whereClause,
        }),
        db.staff.count(),
      ]);

      const discountWithPriceCount = staffs.map(({ password, cafeteriaOrders, ...staff }) => {
        const unpaidCredit = cafeteriaOrders.reduce((total, order) => {
          return total + Number(order.totalAmount);
        }, 0);

        return {
          ...staff,
          codeUsage: staff.referralCode
            ? staff.referralCode._count.referralUsages
            : 0,
          unpaidCredit,
        };
      });

      return {
        staffs: discountWithPriceCount,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
      };
    } catch (error) {
      logger.error('Error getting all staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch staff', 400);
    }
  },

  createStaff: async (staffId: number, reqBody: CreateStaffRequestBody) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);

      const {
        email,
        staffCode,
        fullName,
        locationId,
        departmentId,
        type,
        phoneNumber,
        rewardIds,
        roleIds,
        isDoctor,
        specialtyId,
        isConsultant,
        isVisitingConsultant,
        ...rest
      } = reqBody;

      if (!roleIds || roleIds.length === 0) {
        throw new HttpError('At least one role is required', 400);
      }

      const formattedString = formatString.trimString(staffCode);

      const existingStaffCode = await db.staff.findUnique({
        where: { staffCode: formattedString },
      });

      if (existingStaffCode) {
        throw new HttpError('Staff code already exists', 400);
      }

      const existingStaff = await db.staff.findFirst({
        where: {
          OR: [
            { email: formatString.formatEmail(email) },
            { fullName: fullName.trim() },
          ],
        },
      });

      if (existingStaff) {
        throw new HttpError(
          'Staff with this email or name already exists',
          400
        );
      }

      const refCode = getInitials(fullName, phoneNumber);
      const staffCreateData: any = {
        data: {
          ...rest,
          fullName: fullName.trim(),
          type: type,
          locationId: Number(locationId),
          departmentId: Number(departmentId),
          phoneNumber,
          email: formatString.formatEmail(email),
          staffCode: formattedString,
          referralCode: {
            create: {
              code: refCode,
            },
          },
          account: {
            create: {
              type: 'STAFF',
            },
          },
        },
      };

      if (rewardIds && rewardIds.length > 0) {
        staffCreateData.data.referralCode.create.reward = {
          connect: rewardIds.map((id: string | number) => ({ id: Number(id) })),
        };
      }

      staffCreateData.data.roles = {
        connect: roleIds.map((id: string | number) => ({ id: Number(id) })),
      };

      const createdStaff = await db.staff.create(staffCreateData);

      if (isDoctor) {
        const doctorProfileData: any = {
          staffId: createdStaff.id,
          isDoctor,
          isConsultant,
          isVisitingConsultant,
        };

        if (specialtyId) {
          doctorProfileData.specialtyId = Number(specialtyId);
        }

        await db.doctorProfile.create({
          data: doctorProfileData,
        });
      }

      // Emit socket notification for new staff creation
      broadcast('new_staff_created', {
        staffId: createdStaff.id,
        fullName: fullName.trim(),
        staffCode: formattedString,
        department: departmentId,
        location: locationId,
        timestamp: new Date(),
      });

      return {
        message: 'Staff created successfully',
      };
    } catch (error) {
      logger.error('Error creating staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create staff', 400);
    }
  },

  adminUpdateStaff: async (staffId: number, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const canManage = await auth.hasPermission(PERMISSIONS.STAFF_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          locationFilter = {
            location: {
              regionId,
            },
          };
        } else {
          const locationId = await auth.getLocationId();
          locationFilter = { locationId };
        }
      }

      // Extract account and staff fields
      const { roleIds, creditLimit, mealVoucher, ...dataToUpdate } = reqBody;

      const checkStaff = await db.staff.findUnique({
        where: {
          id: Number(staffId),
          ...locationFilter,
        },
        include: {
          referralCode: true,
          roles: true,
          account: true,
        },
      });
      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }

      // Handle password change if requested
      if (reqBody.password) {
        dataToUpdate.password = await bcrypt.hash(reqBody.password, 10);
      }

      // Handle roles update if provided
      if (roleIds && Array.isArray(roleIds)) {
        dataToUpdate.roles = {
          set: roleIds.map((id: number) => ({ id: Number(id) })),
        };
      }

      await db.staff.update({
        where: { id: Number(reqBody.id) },
        data: dataToUpdate,
      });

      // Update account fields if provided
      if (
        checkStaff.account &&
        (creditLimit !== undefined || mealVoucher !== undefined)
      ) {
        const accountUpdateData: any = {};
        if (creditLimit !== undefined)
          accountUpdateData.creditLimit = new decimal(creditLimit);
        if (mealVoucher !== undefined)
          accountUpdateData.mealVoucher = new decimal(mealVoucher);

        await db.staff.update({
          where: { id: Number(reqBody.id) },
          data: accountUpdateData,
        });
      }

      if (reqBody.isActive) {
        await db.referralCode.update({
          where: { id: Number(checkStaff?.referralCode?.id) },
          data: {
            isActive: reqBody.isActive,
          },
        });
      }
      await clearStaffCaches();

      // Emit socket notification for staff update
      // sendToUser(Number(reqBody.id), 'profile_updated', {
      //   staffId: Number(reqBody.id),
      //   updatedBy: staffId,
      //   timestamp: new Date(),
      // });

      return {
        message: 'Staff updated successfully',
      };
    } catch (error) {
      logger.error('Error updating staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update staff', 400);
    }
  },

  verifyStaffCode: async (reqBody: any) => {
    try {
      const refCode = formatString.trimString(reqBody.code);
      const checkStaffCode = await db.referralCode.findUnique({
        where: { code: refCode },
      });
      if (!checkStaffCode) {
        throw new HttpError('Referral code does not exist', 400);
      }

      if (!checkStaffCode.isActive) {
        throw new HttpError('Referral code has been deactivated', 400);
      }

      const packageExist = await db.package.findUnique({
        where: {
          id: Number(reqBody.id),
        },
        select: {
          name: true,
          slug: true,
          description: true,
        },
      });
      if (!packageExist) {
        throw new HttpError('Package does not exist', 400);
      }

      return {
        package: packageExist,
        message: 'Referral link generated successfully',
      };
    } catch (error) {
      logger.error('Error verifying staff code:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify staff code', 400);
    }
  },

  updateStaff: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT_OWN);
      const { currentPassword, ...dataToUpdate } = reqBody;

      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
      });
      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }

      // Handle password change if requested
      if (reqBody.password) {
        const isPasswordValid = await bcrypt.compare(
          currentPassword,
          checkStaff.password || ''
        );
        if (!isPasswordValid) {
          throw new HttpError('Enter a valid current password', 400);
        }
        dataToUpdate.password = await bcrypt.hash(reqBody.password, 10);
      }

      await db.staff.update({
        where: { id: Number(staffId) },
        data: dataToUpdate,
      });

      await clearStaffCaches();

      // Emit socket notification for profile update
      sendToUser(Number(staffId), 'profile_updated', {
        staffId: Number(staffId),
        updatedBy: staffId,
        timestamp: new Date(),
      });

      return {
        message: 'Account updated successfully',
      };
    } catch (error) {
      logger.error('Error updating account:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update account', 400);
    }
  },

  checkStaffCode: async (reqBody: any) => {
    try {
      const { phone, email, code } = reqBody;
      const formattedCode = formatString.trimString(code);

      const checkStaffExist = await db.staff.findFirst({
        where: {
          OR: [
            { email: formatString.formatEmail(email) },
            { phoneNumber: formatString.trimString(phone) },
          ],
        },
        include: {
          referralCode: true,
        },
      });
      if (!checkStaffExist) {
        throw new HttpError('Staff does not exist', 400);
      }

      if (checkStaffExist.staffCode !== formattedCode) {
        throw new HttpError('Please check the staff ID entered', 400);
      }

      if (checkStaffExist.isActive === false) {
        throw new HttpError(
          'Sorry! This account has been deactivated. Please contact the admin.',
          400
        );
      }
      return {
        name: checkStaffExist.fullName,
        code: checkStaffExist.referralCode?.code,
      };
    } catch (error) {
      logger.error('Error checking staff code:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check staff code', 400);
    }
  },

  getStaffProfile: async (staffId: number) => {
    try {
      const cacheKey = `staff:profile:${staffId}`;

      // const cachedProfile = await getCache(cacheKey);
      // if (cachedProfile) {
      //   devLog(`Admin profile for ${adminId} retrieved from cache`);
      //   return cachedProfile;
      // }

      const result = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: {
          roles: {
            include: {
              permissions: {
                select: {
                  action: true,
                },
              },
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          department: {
            select: {
              name: true,
            },
          },
          doctorProfile: {
            include: {
              specialty: {
                select: {
                  name: true,
                },
              },
            },
          },
          cafeteriaOrders: {
            where: {
              paymentType: 'CREDIT',
              creditPaid: false,
            },
            select: {
              totalAmount: true,
            },
          },
        },
      });

      if (!result) {
        throw new HttpError('Staff account not found', 404);
      }

      const { password, locationId, departmentId, cafeteriaOrders, ...rest } = result;

      const outstandingCredit = cafeteriaOrders.reduce((total, order) => {
        return total + Number(order.totalAmount);
      }, 0);

      // await setCache(cacheKey, rest);

      return {
        ...rest,
        outstandingCredit,
      };
    } catch (error) {
      logger.error('Error getting admin profile:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch admin profile', 400);
    }
  },

  forgotPassword: async (reqBody: any) => {
    try {
      const { code } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff) {
        throw new HttpError('Staff account cannot be found', 400);
      }
      if (!staff.isActive) {
        throw new HttpError('Staff account is deactivated', 400);
      }
      const name = staff.fullName.split(' ')[0];
      const password = crypto.randomBytes(4).toString('hex');
      await db.staff.update({
        where: { id: staff.id },
        data: { password: bcrypt.hashSync(password, 10) },
      });
      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: staff.email,
        subject: 'Your staff login password',
        template: 'staff-login',
        context: {
          name: toTitleCase(name),
          password: password,
        },
      };
      enqueueSendEmailJob(mailOptions);
      return {
        message: `A new password sent succesfully to your email address - ${staff.email}`,
      };
    } catch (error) {
      logger.error('Error resetting password:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to reset password', 400);
    }
  },

  checkStaffId: async (reqBody: any) => {
    try {
      const { code } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff) {
        throw new HttpError('Staff account cannot be found', 400);
      }
      if (!staff.isActive) {
        throw new HttpError('Staff account is deactivated', 400);
      }
      if (staff && staff.password && !staff.lastLogin) {
        return {
          message: ` Please check your email - ${staff.email} for password`,
          statusCode: 201,
        };
      }
      if (!staff.password) {
        const name = staff.fullName.split(' ')[0];
        const password = crypto.randomBytes(4).toString('hex');
        await db.staff.update({
          where: { id: staff.id },
          data: { password: bcrypt.hashSync(password, 10) },
        });
        const mailOptions = {
          from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
          to: staff.email,
          subject: 'Your staff login password',
          template: 'staff-login',
          context: {
            name: toTitleCase(name),
            password: password,
          },
        };
        enqueueSendEmailJob(mailOptions);
        return {
          message: `Password sent succesfully to your email address - ${staff.email}`,
          statusCode: 201,
        };
      }
      return { message: 'Staff ID verified' };
    } catch (error) {
      logger.error('Error verifying staff ID:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify staff ID', 400);
    }
  },

  loginStaff: async (reqBody: any) => {
    try {
      const { code, password } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff || !staff.isActive) {
        throw new HttpError('Invalid credentials', 400);
      }
      const isPasswordValid = await bcrypt.compare(
        password,
        staff.password || ''
      );
      if (!isPasswordValid) {
        throw new HttpError('Enter a valid password', 400);
      }
      const token = jwt.sign({ id: staff.id }, SECRET_KEY, {
        expiresIn: '7 days',
      });
      await db.staff.update({
        where: { id: staff.id },
        data: { lastLogin: new Date() },
      });

      // Emit socket notification for staff login
      sendToUser(staff.id, 'login_successful', {
        staffId: staff.id,
        fullName: staff.fullName,
        lastLogin: new Date(),
        timestamp: new Date(),
      });

      return {
        sub: staff.id,
        token: token,
      };
    } catch (error) {
      logger.error('Error logging in staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to login staff', 400);
    }
  },

  listRole: async (staffId: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.ROLE_VIEW);
      return db.role.findMany({
        include: {
          permissions: true,
        },
      });
    } catch (error) {
      logger.error('Error listing roles:', error);
      throw new HttpError('Failed to list roles', 400);
    }
  },

  getConsultants: async () => {
    try {
      const result = await db.staff.findMany({
        where: {
          isActive: true,
          doctorProfile: {
            isDoctor: true,
            isConsultant: true,
          },
        },
        select: {
          id: true,
          fullName: true,
        },
      });

      return result.map(({ fullName, ...rest }) => ({
        ...rest,
        name: fullName,
      }));
    } catch (error) {
      logger.error('Error fetching consultants:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch consultants', 400);
    }
  },
};
